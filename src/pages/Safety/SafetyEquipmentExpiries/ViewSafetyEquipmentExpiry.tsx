import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { ScrollView, StyleSheet, Text, View } from 'react-native'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { formatDateShort, warnDays } from '@src/lib/datesAndTime'
import { formatInterval } from '@src/lib/util'
import { renderCategoryName } from '@src/lib/categories'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'
import { useCompletedSafetyEquipmentItems } from '@src/shared-state/VesselSafety/useCompletedSafetyEquipmentItems'
import { SafetyEquipmentExpiryHistoryTable } from '@src/components/_organisms/Safety/SafetyEquipmentExpiries/SafetyEquipmentExpiryHistoryTable'
import { ModifySafetyEquipmentExpiryDrawer } from '@src/components/_organisms/Safety/SafetyEquipmentExpiries/ModifySafetyEquipmentExpiryDrawer'
import { ModifyCompletedSafetyEquipmentExpiryDrawer } from '@src/components/_organisms/Safety/SafetyEquipmentExpiries/ModifyCompletedSafetyEquipmentExpiryDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { SafetyEquipmentTaskCompleted } from '@src/shared-state/VesselSafety/useCompletedSafetyEquipmentItems'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaEmptyDivider } from '@src/components/_atoms/SeaDividers/SeaEmptyDivider'
import { usePathname, useRouter } from 'expo-router'
import { Routes } from '@src/navigation/constants'
import { getRoutePath } from '@src/navigation/utils'
import { useLogger, useServiceContainer } from '@src/providers/ServiceProvider'
import {
  DeleteSafetyEquipmentExpiryDto,
  DeleteSafetyEquipmentExpiryUseCase,
} from '@src/domain/use-cases/safety/DeleteSafetyEquipmentExpiryUseCase'
import { deleteIfConfirmed } from '@src/managers/ConfirmDialogManager/ConfirmDialogManager'

const DESKTOP_ITEMS_WIDTH = '100%'

interface SafetyEquipmentExpiriesProps {
  itemId: string
  vesselId: string
}

const ViewSafetyEquipmentExpiry = ({ itemId, vesselId }: SafetyEquipmentExpiriesProps) => {
  const pathname = usePathname()
  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()

  const logger = useLogger('ViewSafetyEquipmentExpiry', { userId, licenseeId, pathname })

  // Global State
  const safetyEquipmentItems = sharedState.safetyEquipmentItems.use()
  const vesselSafetyItems = sharedState.vesselSafetyItems.use()
  const vesselLocations = sharedState.vesselLocations.use()

  // Internal State
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)
  const [isCompletionModalVisible, setIsCompletionModalVisible] = useState(false)
  const [completionModalMode, setCompletionModalMode] = useState<DrawerMode>(DrawerMode.Create)
  const [selectedCompletion, setSelectedCompletion] = useState<SafetyEquipmentTaskCompleted | undefined>()
  const { isMobileWidth, isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  // Hooks
  const router = useRouter()
  const services = useServiceContainer()

  // Hooks
  const item = safetyEquipmentItems?.byId[itemId]
  const completedSafetyEquipmentItems = useCompletedSafetyEquipmentItems(item)

  const itemName = useMemo(() => {
    return renderCategoryName(item?.itemId, vesselSafetyItems)
  }, [item, vesselSafetyItems])
  const isCritical = useMemo(() => {
    return vesselSafetyItems?.byId[itemId]?.isCritical ?? false
  }, [item, vesselSafetyItems])

  const itemType = useMemo(() => {
    const type = item?.type
    if (type === 'expiring') return 'Expiring'
    if (type === 'nonExpiring') return 'Non-Expiring'
    if (type === 'servicable') return 'Serviceable'

    return type
  }, [item?.type])

  const onHistoryItemPress = (completion: SafetyEquipmentTaskCompleted) => {
    setSelectedCompletion(completion)
    setCompletionModalMode(DrawerMode.Edit)
    setIsCompletionModalVisible(true)
  }

  const handleDelete = useCallback(() => {
    if (!item || !vesselId || !userId || !licenseeId) {
      console.error('Vessel ID, Licensee ID, Item, or User ID is not available')
      return
    }

    const dto: DeleteSafetyEquipmentExpiryDto = {
      id: item.id,
      itemName: itemName,
      vesselId,
    }

    const deleteSafetyEquipmentExpiry = services.get(DeleteSafetyEquipmentExpiryUseCase)

    deleteSafetyEquipmentExpiry
      .execute(dto, userId, licenseeId)
      .then(() =>
        router.navigate({
          pathname: getRoutePath(Routes.SAFETY_EQUIPMENT_EXPIRIES),
          params: {
            vesselId,
          },
        })
      )
      .catch(err => console.error(`Error deleting safety equipment expiry\n ${err.message}`))
  }, [item, vesselId, userId, licenseeId, itemName, services, router])

  const onDelete = useCallback(async () => {
    logger.debug("Clicked 'Delete' secondary action")
    await deleteIfConfirmed({
      onConfirmed: handleDelete,
    })
  }, [handleDelete, logger])

  // Loading state
  if (!safetyEquipmentItems || !item) {
    return <Text>Loading...</Text>
  }

  return (
    <RequirePermissions role={'safetyEquipmentList'} level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView showsVerticalScrollIndicator={false} style={styles.container}>
        <SeaPageCard
          titleComponent={
            <SeaPageCardTitle
              title={itemName}
              files={item.files}
              additionalElements={
                item.dateDue ? (
                  <WhenDueStatus
                    whenDue={item.dateDue}
                    warnDaysThreshold={warnDays.safetyEquipmentExpiries[0]}
                    hasFault={item.hasFault ?? false}
                  />
                ) : undefined
              }
            />
          }
          primaryActionButton={
            item.type !== 'nonExpiring' ? (
              <SeaButton
                onPress={() => {
                  setSelectedCompletion(undefined)
                  setCompletionModalMode(DrawerMode.Create)
                  setIsCompletionModalVisible(true)
                }}
                variant={SeaButtonVariant.Primary}
                label={'Complete'}
                iconOptions={{ icon: 'check' }}
              />
            ) : (
              <></>
            )
          }
          secondaryActionButton={[
            <SeaDownloadButton
              key={'download'}
              // TODO: Add export pdf
              onPress={() => alert('This functionality is not implemented yet')}
            />,
            <SeaDeleteButton key={'Delete'} onPress={onDelete} />,
            <SeaEditButton
              key={'Edit'}
              onPress={() => {
                setIsEditModalVisible(true)
              }}
            />,
          ]}>
          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue label={'Type'} value={itemType} />
                    <SeaLabelValue label={'Quantity'} value={item.quantity} />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'location_on' }}
                      showIcon={true}
                      label={'Location'}
                      value={renderCategoryName(item.locationId, vesselLocations)}
                    />
                    <SeaLabelValue
                      iconOptions={{ icon: 'flag' }}
                      showIcon={true}
                      label={'Critical Equipment'}
                      value={isCritical ? 'Yes' : 'No'}
                    />
                  </SeaStack>

                  {item.type === 'servicable' && (
                    <>
                      <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                        <SeaLabelValue
                          iconOptions={{ icon: 'update' }}
                          showIcon={true}
                          label={'Interval'}
                          value={formatInterval(item.interval)}
                        />
                        <SeaLabelValue
                          iconOptions={{ icon: 'calendar_month' }}
                          showIcon={true}
                          label={'Service Due'}
                          value={formatDateShort(item.dateDue)}
                        />
                      </SeaStack>
                      <SeaStack isCollapsible={true} width={'50%'} align={'start'} gap={isDesktopWidth ? 5 : 0}>
                        <SeaLabelValue label={'Notification'} value={item.emailReminder ? 'Yes' : 'No'} />
                      </SeaStack>
                    </>
                  )}

                  {item.type === 'expiring' && (
                    <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                      <SeaLabelValue
                        iconOptions={{ icon: 'calendar_month' }}
                        showIcon={true}
                        label={'Expiry'}
                        value={formatDateShort(item.dateDue)}
                      />
                      <SeaLabelValue label={'Notification'} value={item.emailReminder ? 'Yes' : 'No'} />
                    </SeaStack>
                  )}
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
          {(item.type === 'servicable' || item.type === 'expiring') && (
            <>
              <SeaEmptyDivider />
              <SeaPageCardContentSection>
                <SeaStack
                  direction={isLargeDesktopWidth ? 'row' : 'column'}
                  gap={10}
                  justify="start"
                  align="start"
                  width={'100%'}
                  style={{ flex: 1 }}>
                  <SeaStack
                    direction="column"
                    gap={isDesktopWidth ? 20 : 10}
                    align={'start'}
                    width={isLargeDesktopWidth ? '70%' : '100%'}>
                    <SeaStack
                      direction="column"
                      align={'start'}
                      width={DESKTOP_ITEMS_WIDTH}
                      gap={isDesktopWidth ? 5 : 0}>
                      <SeaStack
                        isCollapsible={true}
                        width={DESKTOP_ITEMS_WIDTH}
                        direction={'row'}
                        gap={isDesktopWidth ? 5 : 0}>
                        <SeaLabelValue
                          label={item?.type === 'servicable' ? 'Service Task' : 'Expiry Task'}
                          value={item.description}
                          layout={'vertical'}
                        />
                      </SeaStack>
                    </SeaStack>
                  </SeaStack>
                </SeaStack>
              </SeaPageCardContentSection>
            </>
          )}
        </SeaPageCard>

        {(item.type === 'servicable' || item.type === 'expiring') && (
          <View style={styles.historyView}>
            <SeaTypography variant={'cardTitle'}>
              {item.type === 'servicable' ? 'Service History' : 'Expiry Replacement History'}
            </SeaTypography>

            <SafetyEquipmentExpiryHistoryTable items={completedSafetyEquipmentItems} onPress={onHistoryItemPress} />
          </View>
        )}
      </ScrollView>

      {isCompletionModalVisible && (
        <ModifyCompletedSafetyEquipmentExpiryDrawer
          mode={completionModalMode}
          selectedItem={item}
          existingCompletion={selectedCompletion}
          visible={isCompletionModalVisible}
          onClose={() => {
            setIsCompletionModalVisible(false)
            setSelectedCompletion(undefined)
          }}
        />
      )}

      {isEditModalVisible && (
        <ModifySafetyEquipmentExpiryDrawer
          mode={DrawerMode.Edit}
          selectedItem={item}
          visible={isEditModalVisible}
          onClose={() => setIsEditModalVisible(false)}
        />
      )}
    </RequirePermissions>
  )
}

const styles = StyleSheet.create({
  container: {
    height: '100%',
  },
  historyView: {
    paddingHorizontal: 10,
    paddingVertical: 10,
  },
})

export default ViewSafetyEquipmentExpiry
