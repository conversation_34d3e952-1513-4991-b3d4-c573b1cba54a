import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { ScrollView, StyleSheet, View } from 'react-native'
import { sharedState } from '@src/shared-state/shared-state'
import { Incident, IncidentType } from '@src/shared-state/HealthSafety/incidents'
import { extractSearchTerms, formatValue } from '@src/lib/util'
import { formatDatetime } from '@src/lib/datesAndTime'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
  SeaTableRow,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { renderVesselName } from '@src/shared-state/Core/vessels'
import { IncidentStatus } from '@src/components/_molecules/IncidentStatus/IncidentStatus'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { useRouter } from 'expo-router'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { EditIncident } from '@src/components/_organisms/HealthSafety/Incidents/EditIncident'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaFilterSearch } from '@src/components/_atoms/SeaFilterSearch/SeaFilterSearch'
import { VesselFilterDropdown } from '@src/components/_molecules/VesselFilterDropdown/VesselFilterDropdown'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaTableIconCalendar, SeaTableIconVessel } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

interface IncidentsProps {
  headerSubNavigation?: SubNav[]
}

export const Incidents = ({ headerSubNavigation }: IncidentsProps) => {
  const incidents = sharedState.incidents.use()
  const user = sharedState.user.use()
  const vesselId = sharedState.vesselId.use()

  // Hooks
  const router = useRouter()
  const [addIncidentVisible, setAddIncidentVisible] = useState(false)
  const [filterVesselIds, setFilterVesselIds] = useState<string[]>(user?.vesselIds ?? [])
  const [searchValue, setSearchValue] = useState('')
  // TODO: Setting Screen
  // const [showSettings, setShowSettings] = useState(false);

  // TODO: Export
  // const [exportType, setExportType] = useState<ExportType>();
  // // Already Commented out
  // // const [medicalReports, setMedicalReports] = useState<MedicalReport>();

  useEffect(() => {
    user?.vesselIds && setFilterVesselIds(user?.vesselIds)
  }, [user?.vesselIds])

  const filteredIncidents = useMemo(() => {
    let _incidents: Incident[] = []
    const _filteredIncidents: Incident[] = []

    // Filter by selected Vessels
    if (filterVesselIds) {
      const hasIncident = {} as { [id: string]: true }
      filterVesselIds.forEach(vesselId => {
        incidents?.byVesselId[vesselId]?.forEach(incident => {
          if (!hasIncident[incident.id]) {
            hasIncident[incident.id] = true
            _incidents.push(incident)
          }
        })
      })
      if (filterVesselIds.length > 1) {
        _incidents.sort((a, b) => {
          return b.whenEvent - a.whenEvent
        })
      }
    } else {
      _incidents = incidents?.all ?? []
    }

    // Filter by search text
    const terms = extractSearchTerms(searchValue, true)
    if (_incidents) {
      for (const incident of _incidents) {
        if (terms.length > 0) {
          let isMatch = true
          for (const termsItem of terms) {
            if (!incident?.searchText?.includes(termsItem, 0)) {
              isMatch = false
              break
            }
          }
          if (!isMatch) {
            continue
          }
        }
        _filteredIncidents.push(incident)
      }
    }
    return _filteredIncidents
  }, [incidents, filterVesselIds, searchValue])

  /**
   * Get the rows for the Table
   *
   * @param items - the filtered list of incidents
   * @return SeaTableRow<Incident>[]
   */
  const buildRows = useCallback(
    (items: Incident[]): SeaTableRow<Incident>[] => {
      return items.map(item => {
        return {
          data: item,

          onPress: (item: Incident) => {
            return router.navigate({
              pathname: getRoutePath(Routes.INCIDENT_REPORT_VIEW),
              params: {
                incidentId: item.id,
              },
            })
          },
        }
      })
    },
    [router]
  )

  const newIncidentData: Incident = {
    vesselId: vesselId,
    type: '',
    name: '',
    categoryId: '',
    isSensitive: 'n',
    reportedBy: '',
    role: '',
    whoInvolved: '',
    witnesses: '',
    whenEvent: '',
    location: '',
    conditions: '',
    notifiedAuthorities: 'n',
    authority: '',
    propertyDamage: '',
    description: '',
    initialActions: '',
    prevention: '',
  }

  return (
    <ScrollablePageLayout>
      <RequirePermissions licenseePermission="hasIncidents" showDenial={true}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title={'Incident / Event Reports'} />}
          primaryActionButton={
            <RequirePermissions
              key="incidentAccidentMedicalRegister"
              role="incidentAccidentMedicalRegister"
              level={permissionLevels.CREATE}>
              <SeaAddButton
                label={'Report'}
                variant={SeaButtonVariant.Primary}
                onPress={() => setAddIncidentVisible(true)}
              />
            </RequirePermissions>
          }
          secondaryActionButton={[
            <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
            <RequirePermissions
              key="incidentAccidentMedicalRegister"
              role="incidentAccidentMedicalRegister"
              level={permissionLevels.EDIT}>
              <SeaSettingsButton key={'settings'} onPress={() => alert('This functionality is not completed yet')} />
            </RequirePermissions>,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Filter Row */}
        <SeaStack direction={'row'} justify={'between'} gap={10}>
          <SeaStack
            style={{
              flexShrink: 1,
            }}>
            <VesselFilterDropdown vesselIds={filterVesselIds} setVesselIds={setFilterVesselIds} />
          </SeaStack>
          <SeaFilterSearch value={searchValue} onChangeText={setSearchValue} />
        </SeaStack>

        {/* Table View */}
        <View style={styles.tableView}>
          {/* TODO: Filter Tags */}
          <SeaTable columns={buildColumns()} rows={buildRows(filteredIncidents)} />
        </View>

        {addIncidentVisible && (
          <EditIncident
            incident={newIncidentData}
            visible={addIncidentVisible}
            onClose={() => setAddIncidentVisible(false)}
            isNew={true}
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  )
}

/**
 * Get the columns for the Table
 *
 * @return SeaTableColumn<Incident>[]
 */
const buildColumns = (): SeaTableColumn<Incident>[] => {
  return [
    {
      label: '',
      width: 60,
      render: item => <SeaTableImage files={item?.files} />,
      compactModeOptions: {
        isThumbnail: true,
      },
    },
    {
      label: 'Name',
      value: item => item.name,
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
      },
    },
    {
      label: 'Vessel',
      icon: () => <SeaTableIconVessel />,
      value: item => renderVesselName(item.vesselId),
      compactModeOptions: {
        hideRow: true,
      },
    },
    {
      label: 'Report #',
      value: item => item.reportNum,
      compactModeOptions: {
        hideRow: true,
      },
    },
    {
      label: 'Date',
      icon: () => <SeaTableIconCalendar />,
      value: item => formatDatetime(item.whenEvent),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.ExtraLarge,
        },
      },
    },
    {
      label: 'Type',
      value: item => formatValue(IncidentType[item.type]),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.ExtraLarge,
        },
      },
    },
    {
      label: 'Status',
      render: (item, isCompactView) => <IncidentStatus status={item.state} compact={isCompactView} />,
      compactModeOptions: {
        rowPosition: CompactRowPosition.TopRightCorner,
      },
    },
  ]
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  tableView: {
    marginTop: 16,
  },
})
