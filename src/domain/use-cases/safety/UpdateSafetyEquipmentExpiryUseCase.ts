import { inject, injectable } from 'inversify'
import { IUseCase } from '@src/domain/use-cases/UseCase'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { ILogger } from '@src/domain/util/logger/ILogger'
import {
  ISafetyEquipmentExpiryService,
  SafetyEquipmentExpiryService,
} from '@src/domain/services/SafetyEquipmentExpiryService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'
import { IVesselService, VesselService } from '@src/domain/services/VesselService'
import { FirestoreRecord } from '@src/domain/data/FirestoreOperation'

export interface UpdateSafetyEquipmentExpiryDto {
  docId: string
  vesselId: string
  safetyItemId?: string
  isNewSafetyItem?: boolean
  newSafetyItemName?: string
  type: string
  isCriticalEquipment: boolean
  locationId?: string
  isNewLocation?: boolean
  newLocationName?: string
  quantity?: string
  files?: SeaFile[]

  interval?: string
  lastCheck?: number
  expiryDate?: string
  description?: string
  emailReminder?: string
}

export interface ICreateSafetyEquipmentExpiryUseCase extends IUseCase<UpdateSafetyEquipmentExpiryDto> {}

@injectable()
export class UpdateSafetyEquipmentExpiryUseCase implements ICreateSafetyEquipmentExpiryUseCase {
  private readonly logger: ILogger

  constructor(
    @inject(VesselService)
    private readonly vesselService: IVesselService,
    @inject(SafetyEquipmentExpiryService)
    private readonly safetyEquipmentExpiryService: ISafetyEquipmentExpiryService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.logger = logger.scoped('UpdateSafetyEquipmentExpiryUseCase')
  }

  public async execute(dto: UpdateSafetyEquipmentExpiryDto, userId: string, licenseeId: string) {
    this.logger.info('Executing UpdateSafetyEquipmentExpiryUseCase', { dto })

    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'safetyEquipmentItems', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Safety Equipment Expiry',
      maximumBatchSize: 20,
    })

    const categoryRecords: FirestoreRecord[] = []

    if (dto.isNewLocation && dto.newLocationName) {
      const { ref, records } = this.vesselService.createVesselLocation(
        operation,
        dto.newLocationName,
        dto.vesselId,
        userId,
        licenseeId
      )
      dto.locationId = ref.id

      this.logger.info(`Creating a new Vessel Location: [${dto.newLocationName}] with ID: ${ref.id}`, {
        records,
      })

      categoryRecords.push(...records)
    }

    if (dto.isNewSafetyItem && dto.newSafetyItemName) {
      const { ref, records } = this.vesselService.createVesselSafetyItem(
        operation,
        dto.newSafetyItemName,
        dto.vesselId,
        userId,
        licenseeId
      )
      dto.safetyItemId = ref.id

      this.logger.info(`Creating a new Safety Equipment Item: [${dto.newSafetyItemName}] with ID: ${ref.id}`, {
        records,
      })

      categoryRecords.push(...records)
    }

    const { ref: safetyEquipmentExpiryRef, records } = this.safetyEquipmentExpiryService.updateSafetyEquipmentExpiry(
      operation,
      dto,
      userId,
      licenseeId
    )
    this.logger.info('Updating existing Safety Equipment Expiry', { records })

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'safetyEquipmentItems',
      safetyEquipmentExpiryRef.id,
      'TODO - Details' // TODO - This needs to be the name e.g. EPIRB, Life Raft
    )
    this.logger.info('Creating Action Log record', { record: actionLogRecord })

    operation.addMany(records).addMany(categoryRecords).add(actionLogRecord)

    await operation.commit()
  }
}
