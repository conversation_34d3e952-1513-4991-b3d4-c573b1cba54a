import React from 'react'
import { SeaDropdown, SeaDropdownProps } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { formatInterval } from '@src/lib/datesAndTime'

interface SeaIntervalDropdownProps extends Omit<SeaDropdownProps<string>, 'items'> {}

export const SeaIntervalDropdown = ({
  onSelect,
  value,
  initialValue,
  label,
  hasError,
  errorText,
  style,
}: SeaIntervalDropdownProps) => {
  return (
    <SeaDropdown
      items={[
        { value: '', label: 'Not Set' },
        { value: '1d', label: formatInterval('1d') },
        { value: '7d', label: formatInterval('7d') },
        { value: '14d', label: formatInterval('14d') },
        { value: '1m', label: formatInterval('1m') },
        { value: '5w', label: formatInterval('5w') },
        { value: '2m', label: formatInterval('2m') },
        { value: '3m', label: formatInterval('3m') },
        { value: '4m', label: formatInterval('4m') },
        { value: '5m', label: formatInterval('5m') },
        { value: '6m', label: formatInterval('6m') },
        { value: '9m', label: formatInterval('9m') },
        { value: '12m', label: formatInterval('12m') },
        { value: '18m', label: formatInterval('18m') },
        { value: '24m', label: formatInterval('24m') },
        { value: '30m', label: formatInterval('30m') },
        { value: '36m', label: formatInterval('36m') },
        { value: '48m', label: formatInterval('48m') },
        { value: '60m', label: formatInterval('60m') },
        { value: '72m', label: formatInterval('72m') },
        { value: '96m', label: formatInterval('96m') },
        { value: '120m', label: formatInterval('120m') },
        { value: '180m', label: formatInterval('180m') },
      ]}
      value={value}
      onSelect={onSelect}
      style={style}
      initialValue={initialValue}
      label={label}
      showIcon={showIcon}
      labelIconOptions={{
        icon: 'update',
      }}
      hasError={hasError}
      errorText={errorText}
    />
  )
}
