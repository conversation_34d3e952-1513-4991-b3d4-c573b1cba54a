import React, { useState } from 'react'
import { Modal, Platform, StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native'
import { DateTime } from 'luxon'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { ErrorText } from '../ErrorText/ErrorText'
import RNDateTimePicker from '@react-native-community/datetimepicker'
import { hexToRgba } from '@src/lib/util'
import { SeaLabelH4 } from '@src/components/_molecules/SeaLabels/SeaLabelH4'
import { SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'

interface SeaDateTimeProps {
  value: DateTime
  onChange: (date: DateTime) => void
  type: 'date' | 'datetime'
  style?: ViewStyle
  label?: string
  showIcon?: boolean
  labelIconOptions?: SeaIconProps
  disabled?: boolean
  hasError?: boolean
  errorText?: string
  forCustomForm?: boolean // Optional prop for custom form usage
}

export const SeaDateTimeInput: React.FC<SeaDateTimeProps> = ({
  value,
  onChange,
  type,
  style,
  label,
  showIcon,
  disabled = false,
  hasError = false,
  errorText,
  forCustomForm = false, // Default to false if not provided
}) => {
  if (Platform.OS === 'web') {
    return (
      <WebDateTime
        disabled={disabled}
        label={label}
        labelIconOptions={{ icon: 'calendar_month' }}
        showIcon={showIcon}
        value={value}
        onChange={onChange}
        type={type}
        style={style}
        hasError={hasError}
        errorText={errorText}
        forCustomForm={forCustomForm} // Pass the prop to
      />
    )
  }
  return (
    <NativeDateTime
      value={value}
      onChange={onChange}
      type={type}
      style={style}
      label={label}
      labelIconOptions={{ icon: 'calendar_month' }}
      showIcon={showIcon}
      disabled={disabled}
      hasError={hasError}
      errorText={errorText}
      forCustomForm={forCustomForm} // Pass the prop to
    />
  )
}

const styles = StyleSheet.create({
  container: {},
})

const NativeDateTime = ({
  value,
  onChange,
  type,
  style,
  label = 'Due Date',
  labelIconOptions,
  showIcon,
  disabled,
  hasError = false,
  errorText,
  forCustomForm = false, // Default to false if not provided
}: SeaDateTimeProps) => {
  const { theme, styles } = useStyles(webStylesheet)
  const [showPicker, setShowPicker] = useState(false)

  return (
    <>
      <View style={style}>
        <SeaLabelH4 showIcon={showIcon} iconOptions={labelIconOptions}>
          {forCustomForm ? label : label.toUpperCase()}
        </SeaLabelH4>
        <View
          style={[
            styles.container,
            disabled
              ? {
                  opacity: 0.5,
                  backgroundColor: theme.colors.input.disabledBackground,
                }
              : {},
            hasError && styles.containerError,
          ]}>
          <TouchableOpacity onPress={() => !disabled && setShowPicker(true)} disabled={disabled}>
            <SeaTypography variant="body">
              {value
                ? value.toLocaleString(type === 'date' ? DateTime.DATE_MED : DateTime.DATETIME_MED)
                : 'Select date'}
            </SeaTypography>
          </TouchableOpacity>
        </View>
        <ErrorText hasError={hasError} text={errorText} />
      </View>
      <Modal visible={showPicker} transparent animationType="none" onRequestClose={() => setShowPicker(false)}>
        <View
          style={{
            flex: 1,
            backgroundColor: hexToRgba(theme.colors.input.disabledBackground, 0.8),
            justifyContent: 'center',
            alignItems: styles.container.alignItems,
          }}>
          <View
            style={{
              backgroundColor: theme.colors.input.background,
              borderRadius: styles.container.borderRadius,
            }}>
            <RNDateTimePicker
              value={value.toJSDate()}
              mode={type}
              display={Platform.OS === 'ios' ? 'inline' : 'default'}
              onChange={event => {
                setShowPicker(false)
                if (event.type === 'set') {
                  onChange(DateTime.fromMillis(event.nativeEvent.timestamp))
                }
              }}
              themeVariant={'light'}
              textColor={theme.colors.text.primary}
              disabled={disabled}
              style={
                {
                  // backgroundColor: "magenta",
                }
              }
            />
          </View>
        </View>
      </Modal>
    </>
  )
}

const WebDateTime = ({
  value,
  onChange,
  type = 'date',
  style,
  label = 'Due Date',
  labelIconOptions,
  showIcon,
  disabled = false,
  hasError = false,
  errorText,
  forCustomForm = false, // Default to false if not provided
}: {
  value: DateTime
  onChange: (value: DateTime) => void
  type: 'date' | 'datetime'
  style?: ViewStyle
  label?: string
  labelIconOptions?: SeaIconProps
  showIcon?: boolean
  disabled?: boolean
  hasError?: boolean
  errorText?: string
  forCustomForm?: boolean // Optional prop for custom form usage
}) => {
  const { styles, theme } = useStyles(webStylesheet)
  return (
    <View style={style}>
      <SeaLabelH4 showIcon={showIcon} iconOptions={labelIconOptions}>
        {forCustomForm ? label : label.toUpperCase()}
      </SeaLabelH4>
      <View
        style={[
          styles.container,
          disabled
            ? {
                opacity: 0.5,
                backgroundColor: theme.colors.input.disabledBackground,
              }
            : {},
          hasError && styles.containerError,
        ]}>
        <input
          disabled={disabled}
          type={type === 'date' ? 'date' : 'datetime-local'}
          value={type === 'date' ? (value.toISODate() ?? '') : (value.toLocal().toFormat("yyyy-MM-dd'T'HH:mm") ?? '')}
          onChange={e => {
            console.log('Value:', e.target.value)
            onChange(DateTime.fromISO(e.target.value))
          }}
          style={styles.input}
        />
      </View>
      <ErrorText hasError={hasError} text={errorText} />
    </View>
  )
}

const ROW_HEIGHT = 40
const webStylesheet = createStyleSheet(theme => ({
  container: {
    flexDirection: 'row',
    height: ROW_HEIGHT,
    width: '100%',
    backgroundColor: theme.colors.input.background,
    borderColor: theme.colors.borderColor,
    borderRadius: 8,
    borderWidth: 1,
    gap: 10,
    paddingHorizontal: 12,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  containerError: {
    borderColor: theme.colors.status.errorPrimary,
  },
  input: {
    // @ts-ignore outlineStyle is a web-only style prop
    outlineStyle: 'none',
    borderWidth: 0,
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    fontSize: 15,
    fontWeight: '400',
    color: theme.colors.text.primary,
  },
}))
