import React, { useMemo, useState } from 'react'
import { KeyboardAvoidingView, StyleSheet, ViewStyle } from 'react-native'
import { DrawerMode, SeaDrawer } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaCheckbox } from '@src/components/_atoms/_inputs/SeaCheckbox/SeaCheckbox'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { DateTime, Duration } from 'luxon'
import { useLogger, useServiceContainer } from '@src/providers/ServiceProvider'
import { useFormik } from 'formik'
import Yup from '@src/lib/yup'
import { useLicenseeSettings } from '@src/hooks/useLicenseeSettings'
import { SeaDurationInput } from '@src/components/_atoms/_inputs/SeaDurationInput/SeaDurationInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { SeaFile } from '@src/lib/fileImports'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { sharedState } from '@src/shared-state/shared-state'
import {
  UpdateCompletedSafetyCheckDto,
  UpdateCompletedSafetyCheckUseCase,
} from '@src/domain/use-cases/safety/UpdateCompletedSafetyCheckUseCase'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import {
  CompleteSafetyCheckDto,
  CompleteSafetyCheckUseCase,
} from '@src/domain/use-cases/safety/CompleteSafetyCheckUseCase'
import { SafetyCheckItem } from '@src/shared-state/VesselSafety/safetyCheckItems'
import { SafetyCheckCompleted } from '@src/shared-state/VesselSafety/useCompletedSafetyCheckItems'
import {
  ModifyDrawerSeaStackColumn,
  ModifyDrawerSeaStackContent,
  ModifyDrawerSeaStackRow,
} from '@src/components/_molecules/ModifyDrawer/ModifyDrawerSeaStacks'
import { SeaCloseButton } from '@src/components/_molecules/IconButtons/SeaCloseButton'

interface ModifyCompletedSafetyCheckDrawerProps {
  visible: boolean
  onClose: () => void
  mode: DrawerMode
  safetyCheck: SafetyCheckItem
  completedSafetyCheck?: SafetyCheckCompleted
  style?: ViewStyle
}

const validationSchema = Yup.object({
  whenCompleted: Yup.number().required(),
})

const useInitialCompletedSafetyCheckValues = (completedSafetyCheck?: SafetyCheckCompleted) => {
  return useMemo(() => {
    if (!completedSafetyCheck) {
      return {
        whenCompleted: DateTime.now().toMillis(),
        notes: '',
        actualTime: 0,
        shouldReportFault: false,
      }
    }

    return {
      whenCompleted: completedSafetyCheck.whenCompleted,
      notes: completedSafetyCheck.notes ?? '',
      actualTime: completedSafetyCheck.actualTime ?? 0,
      shouldReportFault: completedSafetyCheck.shouldReportFault,
    }
  }, [completedSafetyCheck])
}

export const ModifyCompletedSafetyCheckDrawer: React.FC<ModifyCompletedSafetyCheckDrawerProps> = ({
  visible,
  onClose,
  mode,
  safetyCheck,
  completedSafetyCheck,
  style,
}) => {
  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()
  const vesselId = sharedState.vesselId.use()
  const logger = useLogger(`ModifyCompletedSafetyCheckDrawer:${mode}`, { userId, licenseeId, vesselId })

  const [isDrawerReadOnly, setIsDrawerReadOnly] = useState(mode === DrawerMode.Edit)

  const drawerTitle = useMemo(() => {
    if (mode === DrawerMode.Create) return 'Complete Safety Check'

    return isDrawerReadOnly ? 'View Completed Safety Check' : 'Edit Completed Safety Check'
  }, [mode, isDrawerReadOnly])

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const { hasTimeTrackingEnabled } = useLicenseeSettings()

  const user = sharedState.user.use()

  const initialValues = useInitialCompletedSafetyCheckValues(completedSafetyCheck)

  const { values, handleChange, setFieldValue, handleSubmit, resetForm, errors, touched } = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => doSubmit(values),
    enableReinitialize: true,
  })

  const serviceContainer = useServiceContainer()
  const completeSafetyCheckUseCase = serviceContainer.get(CompleteSafetyCheckUseCase)
  const updateCompletedSafetyCheckUseCase = serviceContainer.get(UpdateCompletedSafetyCheckUseCase)

  const executeCreate = (values: typeof initialValues, vesselId: string, userId: string, licenseeId: string) => {
    logger.info('Completing Safety Check', { safetyCheck, values })

    const dto: CompleteSafetyCheckDto = {
      vesselId,
      safetyCheckId: safetyCheck.id,
      interval: safetyCheck.interval,
      itemId: safetyCheck.itemId,
      notes: values.notes,
      whenCompleted: values.whenCompleted,
      shouldReportFault: values.shouldReportFault ?? false,
      actualTime: values.actualTime,
      files: files,
    }

    completeSafetyCheckUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Safety Check completed successfully')
      })
      .catch(err => logger.error(`Error completing Safety Check\n ${err.message}`, err))
  }

  const executeUpdate = (values: typeof initialValues, vesselId: string, userId: string, licenseeId: string) => {
    logger.info('Updating Completed Safety Check', { completedSafetyCheck, values })

    if (!completedSafetyCheck) {
      throw new Error('Cannot update completed safety check without required data')
    }

    const dto: UpdateCompletedSafetyCheckDto = {
      completedSafetyCheckId: completedSafetyCheck.id,
      vesselId,
      safetyCheckId: completedSafetyCheck.safetyCheckId,
      whenCompleted: values.whenCompleted,
      notes: values.notes,
      files: files,
      actualTime: values.actualTime,
      shouldReportFault: values.shouldReportFault,
      interval: safetyCheck.interval,
    }

    updateCompletedSafetyCheckUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Completed Safety Check updated successfully')
        resetForm()
        onClose()
      })
      .catch(err => logger.error(`Error updating Completed Safety Check\n ${err.message}`, err))
  }

  const doSubmit = (values: typeof initialValues) => {
    if (!user?.id || !licenseeId || !vesselId) {
      throw new Error('Missing Licensee or User')
    }

    if (mode === DrawerMode.Create) {
      executeCreate(values, vesselId, user.id, licenseeId)
    }
    if (mode === DrawerMode.Edit) {
      executeUpdate(values, vesselId, user.id, licenseeId)
    }

    resetForm()
    onClose()
  }

  return (
    <SeaDrawer
      title={drawerTitle}
      visible={visible}
      onClose={() => {
        resetForm()
        onClose()
      }}
      readonly={mode === DrawerMode.Edit ? isDrawerReadOnly : false}
      headerActions={
        isDrawerReadOnly
          ? [
              <RequirePermissions key={'edit'} role={'safetyEquipmentChecks'} level={permissionLevels.EDIT}>
                <SeaEditButton key={'edit'} label={'Edit'} onPress={() => setIsDrawerReadOnly(false)} />
              </RequirePermissions>,
            ]
          : []
      }
      primaryAction={
        <RequirePermissions
          key={'edit'}
          role={'safetyEquipmentChecks'}
          level={mode === DrawerMode.Create ? permissionLevels.COMPLETE : permissionLevels.EDIT}>
          <SeaEditButton variant={SeaButtonVariant.Primary} onPress={handleSubmit} label={drawerTitle} />
        </RequirePermissions>
      }
      secondaryAction={<SeaCloseButton variant={SeaButtonVariant.Tertiary} label={'Cancel'} onPress={onClose} />}>
      <KeyboardAvoidingView>
        <ModifyDrawerSeaStackContent>
          {/* Row 1 - Date Completed */}
          <ModifyDrawerSeaStackRow>
            <SeaDateTimeInput
              value={DateTime.fromMillis(values.whenCompleted)}
              onChange={date => setFieldValue('whenCompleted', date.toMillis())}
              type={'date'}
              label={'Date Completed'}
              showIcon={true}
              hasError={Boolean(errors.whenCompleted && touched.whenCompleted)}
              errorText={errors.whenCompleted}
              disabled={mode === DrawerMode.Edit ? isDrawerReadOnly : false}
              style={{ width: '100%', flex: 1 }}
            />
            {/* Column 2 - Actual Time (if time tracking enabled) */}
            <ModifyDrawerSeaStackColumn>
              {hasTimeTrackingEnabled && (
                <SeaDurationInput
                  value={Duration.fromMillis(values.actualTime)}
                  onChange={duration => setFieldValue('actualTime', duration.toMillis())}
                  label={'Actual Time'}
                  showIcon={true}
                  disabled={mode === DrawerMode.Edit ? isDrawerReadOnly : false}
                />
              )}
            </ModifyDrawerSeaStackColumn>
          </ModifyDrawerSeaStackRow>

          {/* Row 2 - Notes */}
          <ModifyDrawerSeaStackRow>
            <SeaTextInput
              label={'Notes'}
              showIcon={true}
              multiLine={true}
              value={values.notes}
              onChangeText={handleChange('notes')}
              disabled={mode === DrawerMode.Edit ? isDrawerReadOnly : false}
            />
          </ModifyDrawerSeaStackRow>

          {/* Row 4 - Report Fault */}
          <ModifyDrawerSeaStackRow>
            <SeaCheckbox
              label={'Report Fault'}
              value={values.shouldReportFault}
              onChange={x => setFieldValue('shouldReportFault', x)}
              disabled={mode === DrawerMode.Edit ? isDrawerReadOnly : false}
              style={{
                width: '100%',
                flex: 1,
              }}
            />
          </ModifyDrawerSeaStackRow>

          {/* Row 5 - Files */}
          <ModifyDrawerSeaStackRow>
            <SeaFileUploader
              initialFiles={completedSafetyCheck?.files ?? []}
              label={'Images / Documents'}
              files={files}
              setFiles={setFiles}
              disabled={mode === DrawerMode.Edit ? isDrawerReadOnly : false}
            />
          </ModifyDrawerSeaStackRow>

          <SeaSpacer height={50} />
        </ModifyDrawerSeaStackContent>
      </KeyboardAvoidingView>
    </SeaDrawer>
  )
}
