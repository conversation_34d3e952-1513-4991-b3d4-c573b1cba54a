import { SeaSelectInput, SeaSelectInputProps } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { CheckBoxActions, SimpleSelectionData } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import React from 'react'

export interface CrewSelectInputProps {
  label: string
  data: SimpleSelectionData
  selectedIds: string[]
  onChange: (selectedIds: string[]) => void
  multiSelect?: boolean
  hasError?: boolean
  errorText?: string
  showIcon?: SeaSelectInputProps<string>['showIcon']
}

export const CrewSelectInput = ({
  label,
  showIcon,
  data,
  selectedIds,
  onChange,
  multiSelect = true,
  hasError,
  errorText,
}: CrewSelectInputProps) => {
  return (
    <SeaSelectInput
      isMulti={multiSelect}
      showSelectAllOption={false}
      selectedItemValues={selectedIds}
      onItemSelect={(action, changedValues) => {
        switch (action) {
          case CheckBoxActions.SELECT:
            return onChange([...selectedIds, changedValues])
          case CheckBoxActions.DESELECT:
            return onChange([...selectedIds].filter(x => x !== changedValues))
        }
      }}
      label={label.toUpperCase()}
      labelIconOptions={{ icon: 'people' }}
      showIcon={showIcon}
      data={data}
      hasError={hasError}
      errorText={errorText}
      style={{ width: '100%', flex: 1 }}
    />
  )
}
