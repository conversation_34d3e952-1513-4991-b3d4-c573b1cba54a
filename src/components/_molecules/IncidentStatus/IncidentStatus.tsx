import React from 'react'
import { IncidentState } from '@src/shared-state/HealthSafety/incidents'
import { SeaStatusType } from '@src/types/Common'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { renderCamelCase } from '@src/lib/util'

interface IncidentStatusProps {
  status: IncidentState
  compact?: boolean
}

export const IncidentStatus = ({ status, compact = false }: IncidentStatusProps) => {
  /**
   * Get the SeaStatusType based on the Incident's/Event's reported status
   *
   * @param statusLabel - status of the Incident
   * @return SeaStatusType - type
   */
  const getMappedVariant = (statusLabel: IncidentState): SeaStatusType => {
    switch (statusLabel) {
      case IncidentState.deleted:
        return SeaStatusType.Minor
      case IncidentState.draft:
        return SeaStatusType.Minor
      case IncidentState.forReview:
        return SeaStatusType.Warning
      case IncidentState.inReview:
        return SeaStatusType.Attention
      case IncidentState.completed:
      default:
        return SeaStatusType.Ok
    }
  }

  const statusVariant = getMappedVariant(status)

  return <SeaStatusPill primaryLabel={renderCamelCase(status)} variant={statusVariant} compact={compact} />
}
