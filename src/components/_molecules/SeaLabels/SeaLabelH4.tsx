import { SeaLabelIcon } from '@src/components/_atoms/SeaLabelValue/SeaLabelIcon'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { StyleSheet, TextStyle, View, ViewStyle } from 'react-native'
import React from 'react'
import { SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'

interface SeaLabelH4Props {
  showIcon?: boolean
  iconOptions?: SeaIconProps
  textStyle?: TextStyle
  containerStyle?: ViewStyle
  children?: React.ReactNode
}

/**
 * This Label component is used to render a label with an icon and a text.
 * Predominantly in the View Pages and Edit Drawers
 */
export const SeaLabelH4 = ({ showIcon, iconOptions, containerStyle, textStyle, children }: SeaLabelH4Props) => {
  return (
    <View style={[styles.labelContainer, containerStyle]}>
      {showIcon && <SeaLabelIcon {...iconOptions} />}
      <SeaTypography
        variant={'label'}
        containerStyle={StyleSheet.flatten([styles.labelTextContainer])}
        textStyle={StyleSheet.flatten([styles.labelText, textStyle])}>
        {children}
      </SeaTypography>
    </View>
  )
}

const styles = StyleSheet.create({
  labelContainer: {
    display: 'flex',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  labelTextContainer: {
    flex: 1,
    marginBottom: 0,
    flexShrink: 1,
  },
  labelText: {
    textAlignVertical: 'center',
    fontWeight: '600',
  },
})
