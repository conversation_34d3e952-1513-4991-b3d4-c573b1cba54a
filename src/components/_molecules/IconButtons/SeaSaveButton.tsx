import { SeaButton, SeaButtonProps, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import React from 'react'

export const SeaSaveButton = (props: SeaButtonProps) => {
  const { variant = SeaButtonVariant.Tertiary, iconOptions, ...rest } = props

  return (
    <SeaButton
      key={'save'}
      variant={variant}
      iconOptions={{
        icon: 'save',
        fill: false,
      }}
      {...rest}
    />
  )
}
