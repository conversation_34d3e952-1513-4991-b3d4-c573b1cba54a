import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaSaveButton } from '@src/components/_molecules/IconButtons/SeaSaveButton'
import React, { useCallback } from 'react'
import { useDeviceWidth } from '@src/hooks/useDevice'

type BasePrimaryActionProps = {
  onSubmit?: () => void
  variant?: SeaButtonVariant
  title?: string
  modelName?: string
}

type PrimaryActionProps = BasePrimaryActionProps

interface ModifyDrawerPrimaryActionProps {
  mode: DrawerMode
  edit: PrimaryActionProps
  create: PrimaryActionProps
}

export const ModifyDrawerPrimaryAction = ({ mode, edit, create }: ModifyDrawerPrimaryActionProps) => {
  const { isMobileWidth } = useDeviceWidth()

  const getTitle = useCallback(
    (actionType: PrimaryActionProps) => {
      let actionTitle = undefined
      if (mode === DrawerMode.Edit) {
        actionTitle = 'Update'
      }

      if (mode === DrawerMode.Create) {
        actionTitle = 'Add'
      }

      if (isMobileWidth) return actionTitle
      if (actionType.title) return actionType.title
      if (actionType.modelName) return `${actionTitle} ${actionType.modelName}`
      return actionTitle
    },
    [mode]
  )

  if (mode === DrawerMode.Edit) {
    return <SeaEditButton variant={SeaButtonVariant.Primary} onPress={edit.onSubmit} label={getTitle(edit)} />
  }

  return <SeaSaveButton variant={SeaButtonVariant.Primary} onPress={create.onSubmit} label={getTitle(create)} />
}
