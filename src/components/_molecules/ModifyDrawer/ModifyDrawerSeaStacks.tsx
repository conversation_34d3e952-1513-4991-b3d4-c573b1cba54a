import { SeaStack, SeaStackProps } from '@src/components/_atoms/SeaStack/SeaStack'
import React from 'react'

export const FULL_WIDTH_DESKTOP = '100%'
export const HALF_WIDTH_DESKTOP = '50%'

export const ModifyDrawerSeaStackContent = ({ children, ...rest }: SeaStackProps) => (
  <SeaStack direction={'column'} align={'start'} gap={10} width={FULL_WIDTH_DESKTOP} {...rest}>
    {children}
  </SeaStack>
)

/**
 * This component is used to render a row in a drawer. It is used to render a row of inputs or other elements.
 */
export const ModifyDrawerSeaStackRow = ({ children, ...rest }: SeaStackProps) => (
  <SeaStack
    direction={'row'}
    align={'start'}
    justify={'between'}
    gap={0}
    width={FULL_WIDTH_DESKTOP}
    isCollapsible={true}
    {...rest}>
    {children}
  </SeaStack>
)

/**
 * This component is used to render a column in a row. It is used as a Placeholder for a column in a row.
 */
export const ModifyDrawerSeaStackColumn = ({
  children,
  style,
  ...rest
}: Omit<SeaStackProps, 'children'> & { children?: React.ReactNode }) => (
  <SeaStack direction={'column'} align={'start'} gap={5} width={HALF_WIDTH_DESKTOP} isCollapsible={true} {...rest}>
    {children}
  </SeaStack>
)
